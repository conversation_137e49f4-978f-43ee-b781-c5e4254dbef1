// Test script untuk memverifikasi auto-check kategori dan disable behavior
// Jalankan di browser console untuk debugging

console.log('🎯 TESTING AUTO-CHECK CATEGORY BEHAVIOR');

// Simulasi context dan filters
const testContext = {
  category: 'Elektronik',
  allSubcategories: [
    { name: 'Konsol Game' },
    { name: '<PERSON><PERSON><PERSON><PERSON>l' },
    { name: 'TV & Audio' },
    { name: 'Kamer<PERSON>' }
  ]
};

// Test scenarios
const testScenarios = [
  {
    name: 'Scenario 1: Select subcategory "Konsol Game"',
    initialFilters: { kategori: [] },
    action: { type: 'kategori', value: 'Konsol Game', checked: true },
    expectedResult: {
      filters: ['Elektronik', 'Konsol Game'],
      mainCategoryDisabled: true,
      description: 'Main category auto-checked and disabled'
    }
  },
  {
    name: 'Scenario 2: Select another subcategory "TV & Audio"',
    initialFilters: { kategori: ['Elektronik', 'Konsol Game'] },
    action: { type: 'kategori', value: 'TV & Audio', checked: true },
    expectedResult: {
      filters: ['Elektronik', 'Konsol Game', 'TV & Audio'],
      mainCategoryDisabled: true,
      description: 'Main category remains disabled with multiple subcategories'
    }
  },
  {
    name: 'Scenario 3: Uncheck "Konsol Game" (but "TV & Audio" still selected)',
    initialFilters: { kategori: ['Elektronik', 'Konsol Game', 'TV & Audio'] },
    action: { type: 'kategori', value: 'Konsol Game', checked: false },
    expectedResult: {
      filters: ['Elektronik', 'TV & Audio'],
      mainCategoryDisabled: true,
      description: 'Main category remains disabled while other subcategory selected'
    }
  },
  {
    name: 'Scenario 4: Uncheck last subcategory "TV & Audio"',
    initialFilters: { kategori: ['Elektronik', 'TV & Audio'] },
    action: { type: 'kategori', value: 'TV & Audio', checked: false },
    expectedResult: {
      filters: [],
      mainCategoryDisabled: false,
      description: 'Main category auto-unchecked and enabled when no subcategories'
    }
  },
  {
    name: 'Scenario 5: Try to uncheck main category while subcategory selected',
    initialFilters: { kategori: ['Elektronik', 'Konsol Game'] },
    action: { type: 'kategori', value: 'Elektronik', checked: false },
    expectedResult: {
      filters: ['Elektronik', 'Konsol Game'],
      mainCategoryDisabled: true,
      description: 'Main category cannot be unchecked while subcategory selected'
    }
  }
];

// Simulate handleFilterChange logic
function simulateFilterChange(currentFilters, action, context) {
  const newFilters = { ...currentFilters };
  if (!newFilters[action.type]) newFilters[action.type] = [];
  
  const isKategoriType = action.type === 'kategori';
  
  if (action.checked) {
    if (!newFilters[action.type].includes(action.value)) {
      newFilters[action.type].push(action.value);
    }
    
    // Auto-check main category when subcategory is selected
    if (isKategoriType && context && context.allSubcategories) {
      const isSubcategory = context.allSubcategories.some(sub => sub.name === action.value);
      
      if (isSubcategory) {
        if (!newFilters[action.type].includes(context.category)) {
          newFilters[action.type].push(context.category);
          console.log('✅ Auto-checked main category:', context.category);
        }
      }
    }
  } else {
    // Handle unchecking
    if (isKategoriType && context && context.allSubcategories) {
      const isMainCategory = action.value === context.category;
      const isSubcategory = context.allSubcategories.some(sub => sub.name === action.value);
      
      // Prevent unchecking main category if any subcategory is still selected
      if (isMainCategory) {
        const hasSelectedSubcategories = context.allSubcategories.some(sub =>
          newFilters[action.type].includes(sub.name)
        );
        
        if (hasSelectedSubcategories) {
          console.log('🚫 Cannot uncheck main category - subcategories still selected');
          return currentFilters; // Don't allow unchecking main category
        }
      }
      
      // If unchecking subcategory, check if it's the last one
      if (isSubcategory) {
        const remainingSubcategories = context.allSubcategories.filter(sub =>
          sub.name !== action.value && newFilters[action.type].includes(sub.name)
        );
        
        // If no more subcategories selected, also uncheck main category
        if (remainingSubcategories.length === 0) {
          newFilters[action.type] = newFilters[action.type].filter(item => item !== context.category);
          console.log('✅ Auto-unchecked main category - no subcategories left');
        }
      }
    }
    
    newFilters[action.type] = newFilters[action.type].filter(item => item !== action.value);
    
    if (newFilters[action.type].length === 0) {
      delete newFilters[action.type];
    }
  }
  
  return newFilters;
}

// Check if main category should be disabled
function isMainCategoryDisabled(filters, context) {
  if (!context || !context.allSubcategories) return false;
  
  return context.allSubcategories.some(sub =>
    filters.kategori?.includes(sub.name)
  );
}

// Run tests
console.log('🧪 RUNNING TESTS:');
console.log('================');

testScenarios.forEach((scenario, index) => {
  console.log(`\n${index + 1}. ${scenario.name}`);
  console.log('Initial filters:', scenario.initialFilters);
  console.log('Action:', scenario.action);
  
  const resultFilters = simulateFilterChange(scenario.initialFilters, scenario.action, testContext);
  const isDisabled = isMainCategoryDisabled(resultFilters, testContext);
  
  console.log('Result filters:', resultFilters);
  console.log('Main category disabled:', isDisabled);
  console.log('Expected:', scenario.expectedResult);
  
  // Verify results
  const actualFilters = resultFilters.kategori || [];
  const expectedFilters = scenario.expectedResult.filters;
  const filtersMatch = JSON.stringify(actualFilters.sort()) === JSON.stringify(expectedFilters.sort());
  const disabledMatch = isDisabled === scenario.expectedResult.mainCategoryDisabled;
  
  if (filtersMatch && disabledMatch) {
    console.log('✅ TEST PASSED');
  } else {
    console.log('❌ TEST FAILED');
    console.log('  Filters match:', filtersMatch);
    console.log('  Disabled match:', disabledMatch);
  }
  
  console.log('Description:', scenario.expectedResult.description);
  console.log('---');
});

console.log('\n🎯 SUMMARY:');
console.log('✅ When subcategory selected → Main category auto-checked and disabled');
console.log('✅ When multiple subcategories → Main category remains disabled');
console.log('✅ When last subcategory unchecked → Main category auto-unchecked and enabled');
console.log('🚫 Main category cannot be unchecked while subcategories are selected');

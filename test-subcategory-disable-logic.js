/**
 * Test Case: Subcategory Disable Logic saat Filter Non-Kategori
 * 
 * Men<PERSON>ji implementasi logic untuk disable subcategory ketika:
 * - <PERSON>ya ada satu subcategory yang terchecked
 * - Filter non-kategori sedang diterapkan
 */

class SubcategoryDisableTest {
  constructor() {
    this.testResults = [];
  }

  /**
   * Mock subcategory context
   */
  mockSubcategoryContext = {
    category: "Elektronik",
    selectedSubcategory: "Konsol Game",
    allSubcategories: [
      { id: "1", name: "Konsol Game" },
      { id: "2", name: "<PERSON><PERSON><PERSON><PERSON> Konsol" },
      { id: "3", name: "TV & Aksesoris" },
      { id: "4", name: "Perangkat Audio & Speaker" }
    ]
  };

  /**
   * Test 1: Subcategory with no products after filter = DISABLED
   */
  testSubcategoryWithNoProductsAfterFilter() {
    console.log('🧪 TEST 1: Subcategory with no products after filter');

    const tempFilters = {
      kategori: ["Konsol Game", "Aksesoris Konsol"], // Two subcategories checked
      'rentang harga': ["Rp 100.000 - Rp 500.000"] // Non-category filter applied
    };

    // Mock scenario: "Konsol Game" has 0 products after filter, but is still checked
    const result = this.checkDisabledLogic("Konsol Game", tempFilters, true, 0, [
      { name: "Konsol Game", count: 0 },
      { name: "Aksesoris Konsol", count: 3 }
    ]);

    const expected = true; // Should be disabled (no products after filter)
    const passed = result === expected;

    this.testResults.push({
      test: "Subcategory with no products after filter",
      expected,
      actual: result,
      passed,
      reason: "Subcategory has 0 products after non-category filter applied"
    });

    console.log(`✅ Expected: ${expected}, Actual: ${result}, Passed: ${passed}`);
    return passed;
  }

  /**
   * Test 2: Last subcategory with products = DISABLED (prevent empty results)
   */
  testLastSubcategoryWithProducts() {
    console.log('🧪 TEST 2: Last subcategory with products');

    const tempFilters = {
      kategori: ["Konsol Game", "Aksesoris Konsol"], // Two subcategories checked
      'rentang harga': ["Rp 100.000 - Rp 500.000"] // Non-category filter applied
    };

    // Mock scenario: Only "Aksesoris Konsol" has products after filter
    const result = this.checkDisabledLogic("Aksesoris Konsol", tempFilters, true, 3, [
      { name: "Konsol Game", count: 0 },
      { name: "Aksesoris Konsol", count: 3 }
    ]);

    const expected = true; // Should be disabled (last subcategory with products)
    const passed = result === expected;

    this.testResults.push({
      test: "Last subcategory with products",
      expected,
      actual: result,
      passed,
      reason: "Only subcategory with products after filter, disable to prevent empty results"
    });

    console.log(`✅ Expected: ${expected}, Actual: ${result}, Passed: ${passed}`);
    return passed;
  }

  /**
   * Test 3: Multiple subcategories with products = ENABLED
   */
  testMultipleSubcategoriesWithProducts() {
    console.log('🧪 TEST 3: Multiple subcategories with products');

    const tempFilters = {
      kategori: ["Konsol Game", "Aksesoris Konsol"], // Two subcategories checked
      'rentang harga': ["Rp 100.000 - Rp 500.000"] // Non-category filter applied
    };

    // Mock scenario: Both subcategories have products after filter
    const result = this.checkDisabledLogic("Konsol Game", tempFilters, true, 2, [
      { name: "Konsol Game", count: 2 },
      { name: "Aksesoris Konsol", count: 3 }
    ]);

    const expected = false; // Should be enabled (multiple subcategories have products)
    const passed = result === expected;

    this.testResults.push({
      test: "Multiple subcategories with products",
      expected,
      actual: result,
      passed,
      reason: "Multiple subcategories have products, so should remain enabled"
    });

    console.log(`✅ Expected: ${expected}, Actual: ${result}, Passed: ${passed}`);
    return passed;
  }

  /**
   * Test 4: Zero count product = DISABLED (existing logic)
   */
  testZeroCountProduct() {
    console.log('🧪 TEST 4: Zero count product');
    
    const tempFilters = {
      kategori: ["Konsol Game"]
    };

    // Simulate zero count by passing count = 0
    const result = this.checkDisabledLogic("Konsol Game", tempFilters, true, 0);
    
    const expected = true; // Should be disabled due to zero count
    const passed = result === expected;
    
    this.testResults.push({
      test: "Zero count product",
      expected,
      actual: result,
      passed,
      reason: "Product count is zero, so should be disabled"
    });

    console.log(`✅ Expected: ${expected}, Actual: ${result}, Passed: ${passed}`);
    return passed;
  }

  /**
   * Test 5: No non-category filter applied = ENABLED
   */
  testNoNonCategoryFilter() {
    console.log('🧪 TEST 5: No non-category filter applied');

    const tempFilters = {
      kategori: ["Konsol Game"] // Only category filter, no non-category filters
    };

    // Test without non-category filters
    const result = this.checkDisabledLogic("Konsol Game", tempFilters, true, 5, []);

    const expected = false; // Should be enabled (no non-category filters)
    const passed = result === expected;

    this.testResults.push({
      test: "No non-category filter applied",
      expected,
      actual: result,
      passed,
      reason: "No non-category filters applied, so should remain enabled"
    });

    console.log(`✅ Expected: ${expected}, Actual: ${result}, Passed: ${passed}`);
    return passed;
  }

  /**
   * Mock implementation of the disabled logic from the component
   */
  checkDisabledLogic(subcategoryName, tempFilters, isChecked, count = 5, itemsToShow = []) {
    const context = this.mockSubcategoryContext;
    const type = 'kategori';

    // Default: disabled if no products
    let isDisabled = count === 0;

    // FIXED LOGIC: When non-category filters are applied, disable subcategories with 0 count
    // but keep them checked if they were previously selected
    const hasNonCategoryFilters = (tempFilters['rentang harga']?.length || 0) > 0 ||
                                 (tempFilters.rating?.length || 0) > 0 ||
                                 (tempFilters.pengiriman?.length || 0) > 0 ||
                                 (tempFilters.fitur?.length || 0) > 0;

    if (hasNonCategoryFilters && context && context.allSubcategories) {
      // Count how many subcategories have products after filtering
      const subcategoriesWithProducts = itemsToShow.filter(item => item.count > 0);

      // If this subcategory has no products after filtering, disable it but keep it checked
      if (count === 0 && isChecked) {
        isDisabled = true;
        console.log('🔒 MOCK: Disabling subcategory with no products after filtering:', subcategoryName, 'count:', count);
      }

      // Special case: If only one subcategory has products, disable it to prevent unchecking
      if (subcategoriesWithProducts.length === 1 && count > 0 && isChecked) {
        isDisabled = true;
        console.log('🔒 MOCK: Disabling last subcategory with products:', subcategoryName, 'to prevent empty results');
      }
    }

    return isDisabled;
  }

  /**
   * Run all tests
   */
  runAllTests() {
    console.log('🚀 Starting Subcategory Disable Logic Tests...\n');
    
    const tests = [
      () => this.testSubcategoryWithNoProductsAfterFilter(),
      () => this.testLastSubcategoryWithProducts(),
      () => this.testMultipleSubcategoriesWithProducts(),
      () => this.testZeroCountProduct(),
      () => this.testNoNonCategoryFilter()
    ];

    let passedTests = 0;
    tests.forEach((test, index) => {
      console.log(`\n--- Test ${index + 1} ---`);
      if (test()) {
        passedTests++;
      }
    });

    console.log('\n📊 TEST SUMMARY:');
    console.log(`✅ Passed: ${passedTests}/${tests.length}`);
    console.log(`❌ Failed: ${tests.length - passedTests}/${tests.length}`);
    
    if (passedTests === tests.length) {
      console.log('🎉 All tests passed! Implementation is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please review the implementation.');
    }

    // Detailed results
    console.log('\n📋 DETAILED RESULTS:');
    this.testResults.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} Test ${index + 1}: ${result.test}`);
      console.log(`   Expected: ${result.expected}, Actual: ${result.actual}`);
      console.log(`   Reason: ${result.reason}\n`);
    });

    return passedTests === tests.length;
  }
}

// Run tests
const tester = new SubcategoryDisableTest();
tester.runAllTests();

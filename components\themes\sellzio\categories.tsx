'use client'

import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
// ChevronLeft import removed as it's no longer used
import SubcategoryView from './SubcategoryView'

// Interface untuk Product
interface Product {
  id: number;
  name: string;
  shortName: string;
  category: string;
  subcategory: string;
  price: string;
  originalPrice: string;
  discount: string;
  rating: string;
  sold: string;
  shipping: string;
  image: string;
  isMall: boolean;
  cod: boolean;
  storeName: string;
  address: {
    province: string;
    city: string;
    district: string;
    village: string;
  };
  searchScore?: number;
  matchDetails?: string[];
}

// Interface untuk Category
interface Category {
  id: string;
  name: string;
  icon: string;
  color?: string;
  slug?: string;
  banner?: string;
  subcategories?: Category[];
}

// Interface untuk SellzioCategories
interface SellzioCategoriesProps {
  products: Product[];
}

// Interface untuk SubcategoryView
interface SubcategoryViewProps {
  category: Category;
  subcategories: Category[];
  onBack: () => void;
  onSubcategoryClick: (subcategory: Category) => void;
}

// Fungsi untuk generate ID
const generateId = (prefix: string, name: string) => {
  return `${prefix}-${name.toLowerCase().replace(/\s+/g, '-')}`
}

// Fungsi untuk mendapatkan icon - menggunakan emoji seperti SubcategoryView
const getIcon = (iconName: string) => {
  const iconMap: { [key: string]: string } = {
    // Elektronik & Technology
    'device-mobile': '📱',
    'monitor': '💻',
    'camera': '📷',
    'headphones': '🎧',
    'tv': '📺',
    'gamepad': '🎮',
    'speaker': '🔊',
    'microwave': '🔥',
    'washing-machine': '🧺',
    'air-conditioner': '❄️',
    'lamp': '💡',
    'battery': '🔋',
    'thermometer': '🌡️',

    // Fashion & Accessories
    'shirt': '👕',
    'shoes': '👟',
    'bag': '🎒',
    'watch': '⌚',
    'ring': '💍',
    'glasses': '👓',
    'hat': '👒',
    'tie': '👔',

    // Health & Beauty
    'kesehatan': '🏥',
    'activity': '💊',
    'medicine': '💉',
    'first-aid': '🏥',
    'package': '📦',

    // Hobbies & Collections
    'star': '⭐',
    'music': '🎵',
    'archive': '📦',
    'pet': '🐕',
    'game': '🎲',
    'photo': '📸',

    // General
    'shopping-cart': '🛒',
    'shopping-bag': '🛍️',
    'home': '🏠',
    'car': '🚗',
    'book': '📚',
    'utensils': '🍽️',
    'gift': '🧸',
    'smile': '👶',
    'map-pin': '📍',
    'heart': '💄',

    // New category icons
    'smartphone': '📞',
    'tools': '🔧'
  }
  return iconMap[iconName] || '🛒'
}

// Fungsi untuk mendapatkan icon subcategory - unik dan spesifik
const getSubcategoryIcon = (subcategoryName: string) => {
  const subcategoryIcons: { [key: string]: string } = {
    // Elektronik - Icons yang lebih spesifik dan unik
    'Konsol Game': '🎮', 'Aksesoris Konsol': '🕹️', 'Alat Casing': '🔧', 'Foot Bath & Spa': '🛁',
    'Mesin Jahit & Aksesoris': '🧵', 'Setrika & Mesin Uap': '🔥', 'Purifier & Humidifier': '🌬️',
    'Perangkat Debu & Peralatan Perawatan Lantai': '🧹', 'Telepon': '☎️', 'Mesin Cuci & Pengering': '🧺',
    'Water Heater': '🚿', 'Pendingin Ruangan': '❄️', 'Pengering Sepatu': '👢', 'Penghangat Ruangan': '🔥',
    'TV & Aksesoris': '📺', 'Perangkat Dapur': '🍳', 'Lampu': '💡', 'Kamera Keamanan': '📹',
    'Video Game': '🎯', 'Kelastrian': '⚡', 'Baterai': '🔋', 'Rokok Elektronik & Shisha': '💨',
    'Remote Kontrol': '📱', 'Walkie Talkie': '📻', 'Media Player': '📀', 'Perangkat Audio & Speaker': '🎧',
    'Elektronik Lainnya': '⚙️',

    // Komputer & Aksesoris - Icons yang lebih beragam
    'Desktop': '🖥️', 'Monitor': '🖨️', 'Komponen Desktop & Laptop': '🔌', 'Penyimpanan Data': '💾',
    'Komponen Network': '🌐', 'Software': '💿', 'Peralatan Kantor': '📋', 'Printer & Scanner': '🖨️',
    'Aksesoris Desktop & Laptop': '🖱️', 'Keyboard & Mouse': '⌨️', 'Laptop': '💻', 'Gaming': '🎮',
    'Audio Computer': '🔊', 'Proyektor & Aksesoris': '📽️', 'Komputer & Aksesoris Lainnya': '💽',

    // Handphone & Aksesoris - Icons yang lebih spesifik
    'Kartu Perdana': '📶', 'Tablet': '📱', 'Handphone': '📞', 'Perangkat Wearable': '⌚',
    'Perangkat VR': '🥽', 'Aksesoris Selfie': '🤳', 'Handphone & Tablet Aksesoris': '📲',
    'Kartu Memori': '💳', 'Kabel, Charger, & Konverter': '🔌', 'Powerbank & Baterai': '🔋',
    'Casing & Skin': '📱', 'Audio Handphone': '🎵', 'Handphone & Aksesoris Lainnya': '📳',

    // Pakaian Pria - Icons yang lebih beragam
    'Denim': '👖', 'Hoodie & Sweatshirt': '🧥', 'Sweater & Cardigan': '🧶', 'Jaket, Mantel, & Rompi': '🧥',
    'Jas Formal': '🤵', 'Celana Panjang': '👖', 'Celana Pendek': '🩳', 'Atasan': '👕', 'Batik': '🎨',
    'Pakaian Dalam': '🩲', 'Pakaian Tidur': '🛌', 'Set Pakaian Pria': '👔', 'Pakaian Tradisional': '🥻',
    'Kostum': '🎭', 'Pakaian Kerja': '👷', 'Pakaian Pria Lainnya': '👨',

    // Sepatu Pria - Icons yang lebih spesifik
    'Sneakers': '👟', 'Kaos Kaki': '🧦', 'Sandal': '👡', 'Aksesoris & Perawatan Sepatu': '🧽',
    'Boot': '🥾', 'Tall Sepatu': '👢', 'Slip-On & Mules': '🥿', 'Sepatu Pria Lainnya': '👞',
    'Sepatu Formal': '👞',

    // Tas Pria - Icons yang lebih beragam
    'Tas Selempang & Bahu Pria': '🎒', 'Dompet': '💳', 'Ransel Pria': '🎒', 'Tas Pinggang Pria': '👝',
    'Tas Laptop': '💼', 'Clutch': '👛', 'Tote Bag': '🛍️', 'Tas Kerja': '💼', 'Tas Pria Lainnya': '🧳',

    // Aksesoris Fashion - Icons yang lebih spesifik
    'Cincin': '💍', 'Anting': '👂', 'Syal & Selendang': '🧣', 'Sarung Tangan': '🧤',
    'Aksesoris Rambut': '💇', 'Gelang Tangan & Bangle': '📿', 'Gelang Kaki': '🦶', 'Topi': '👒',
    'Kalung': '📿', 'Kacamata & Aksesoris': '👓', 'Lensa Kontak & Aksesoris': '👁️', 'Logam Mulia': '🥇',
    'Ikat Pinggang': '🔗', 'Dasi': '👔', 'Aksesoris Tambahan': '✨', 'Set & Paket Aksesoris': '🎁',
    'Perhiasan Berharga': '💎', 'Aksesoris Fashion Lainnya': '🎀',

    // Jam Tangan - Icons yang lebih beragam
    'Jam Tangan Wanita': '⌚', 'Jam Tangan Pria': '⏰', 'Jam Tangan Couple': '💕',
    'Aksesoris Jam Tangan': '🔗', 'Jam Tangan Lainnya': '⏱️',

    // Kesehatan - Icons yang lebih spesifik
    'Kewanitaan': '🌸', 'Kesehatan Seksual': '💗', 'Perawatan Mata': '👁️', 'Suplemen Makanan': '💊',
    'Obat-obatan & Alat Kesehatan': '🩺', 'Alat Tes & Monitor': '📊', 'P3K': '🚑',
    'Alat Bantu Cedera & Disabilitas': '🦽', 'Obat Nyamuk': '🦟', 'Popok Dewasa': '🧷',
    'Hand Sanitizer': '🧴', 'Minyak Esensial': '🌿', 'Perawatan Hidung & Pernapasan': '👃',
    'Perawatan Telinga': '👂', 'Perawatan Mulut': '🦷', 'Kesehatan Lainnya': '⚕️',

    // Hobi & Koleksi - Icons yang lebih beragam
    'Aksesoris Hewan Peliharaan': '🐕', 'Litter & Toilet': '🚽', 'Grooming Hewan': '✂️',
    'Pakaian & Aksesoris Hewan': '🦴', 'Perawatan Kesehatan Hewan': '🏥', 'Makanan Hewan': '🥫',
    'Koleksi': '🗃️', 'Mainan & Games': '🎲', 'CD, DVD & Bluray': '💿', 'Alat & Aksesoris Musik': '🎸',
    'Piringan Hitam': '📀', 'Album Foto': '📸', 'Perlengkapan Menjahit': '🧵', 'Hobi & Koleksi Lainnya': '🎨',

    'default': '🛒'
  };

  return subcategoryIcons[subcategoryName] || subcategoryIcons['default'];
}

// Data kategori - dengan icon yang unik dan sesuai
const allCategories: Category[] = [
  { id: generateId('kategori', 'elektronik'), name: "Elektronik", icon: 'tv', color: "#1BA0E2" },
  { id: generateId('kategori', 'komputer-aksesoris'), name: "Komputer & Aksesoris", icon: 'monitor', color: "#5D6D7E" },
  { id: generateId('kategori', 'handphone-aksesoris'), name: "Handphone & Aksesoris", icon: 'device-mobile', color: "#1BA0E2" },
  { id: generateId('kategori', 'pakaian-pria'), name: "Pakaian Pria", icon: 'shirt', color: "#F1C40F" },
  { id: generateId('kategori', 'sepatu-pria'), name: "Sepatu Pria", icon: 'shoes', color: "#E67E22" },
  { id: generateId('kategori', 'tas-pria'), name: "Tas Pria", icon: 'bag', color: "#8E44AD" },
  { id: generateId('kategori', 'aksesoris-fashion'), name: "Aksesoris Fashion", icon: 'ring', color: "#E84393" },
  { id: generateId('kategori', 'jam-tangan'), name: "Jam Tangan", icon: 'watch', color: "#F39C12" },
  { id: generateId('kategori', 'kesehatan'), name: "Kesehatan", icon: 'kesehatan', color: "#2ECC71" },
  { id: generateId('kategori', 'hobi-koleksi'), name: "Hobi & Koleksi", icon: 'pet', color: "#1ABC9C" }
]

// Helper function to add category to products
const addCategoryToProducts = (products: any[], categoryName: string) => {
  return products.map(product => ({
    ...product,
    category: categoryName
  }));
};

// REMOVED: Data produk dipindahkan ke sampleProducts di page.tsx untuk satu sumber data
// Function to get products from main page data
const getProductsFromMainPage = (subcategoryName: string) => {
  // This will be populated by the main page's sampleProducts
  // We'll use window event to get the data
  return [];
};

// REMOVED: Semua data produk dipindahkan ke sampleProducts di page.tsx
// Sekarang menggunakan satu sumber data tunggal
const subcategoryProducts: { [key: string]: any[] } = {};

// Data subkategori - sesuai data yang diberikan
const subCategories: { [key: string]: Category[] } = {
  "Elektronik": [
    { id: generateId('elektronik', 'konsol-game'), name: "Konsol Game", icon: 'gamepad', color: "#1BA0E2" },
    { id: generateId('elektronik', 'aksesoris-konsol'), name: "Aksesoris Konsol", icon: 'gamepad', color: "#1BA0E2" },
    { id: generateId('elektronik', 'alat-casing'), name: "Alat Casing", icon: 'tools', color: "#1BA0E2" },
    { id: generateId('elektronik', 'foot-bath-spa'), name: "Foot Bath & Spa", icon: 'health', color: "#1BA0E2" },
    { id: generateId('elektronik', 'mesin-jahit'), name: "Mesin Jahit & Aksesoris", icon: 'tools', color: "#1BA0E2" },
    { id: generateId('elektronik', 'setrika'), name: "Setrika & Mesin Uap", icon: 'microwave', color: "#1BA0E2" },
    { id: generateId('elektronik', 'purifier'), name: "Purifier & Humidifier", icon: 'air-conditioner', color: "#1BA0E2" },
    { id: generateId('elektronik', 'perangkat-debu'), name: "Perangkat Debu & Peralatan Perawatan Lantai", icon: 'tools', color: "#1BA0E2" },
    { id: generateId('elektronik', 'telepon'), name: "Telepon", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('elektronik', 'mesin-cuci'), name: "Mesin Cuci & Pengering", icon: 'washing-machine', color: "#1BA0E2" },
    { id: generateId('elektronik', 'water-heater'), name: "Water Heater", icon: 'thermometer', color: "#1BA0E2" },
    { id: generateId('elektronik', 'pendingin-ruangan'), name: "Pendingin Ruangan", icon: 'air-conditioner', color: "#1BA0E2" },
    { id: generateId('elektronik', 'pengering-sepatu'), name: "Pengering Sepatu", icon: 'shoes', color: "#1BA0E2" },
    { id: generateId('elektronik', 'penghangat-ruangan'), name: "Penghangat Ruangan", icon: 'thermometer', color: "#1BA0E2" },
    { id: generateId('elektronik', 'tv-aksesoris'), name: "TV & Aksesoris", icon: 'tv', color: "#1BA0E2" },
    { id: generateId('elektronik', 'perangkat-dapur'), name: "Perangkat Dapur", icon: 'microwave', color: "#1BA0E2" },
    { id: generateId('elektronik', 'lampu'), name: "Lampu", icon: 'lamp', color: "#1BA0E2" },
    { id: generateId('elektronik', 'kamera-keamanan'), name: "Kamera Keamanan", icon: 'camera', color: "#1BA0E2" },
    { id: generateId('elektronik', 'video-game'), name: "Video Game", icon: 'gamepad', color: "#1BA0E2" },
    { id: generateId('elektronik', 'kelastrian'), name: "Kelastrian", icon: 'battery', color: "#1BA0E2" },
    { id: generateId('elektronik', 'baterai'), name: "Baterai", icon: 'battery', color: "#1BA0E2" },
    { id: generateId('elektronik', 'rokok-elektronik'), name: "Rokok Elektronik & Shisha", icon: 'health', color: "#1BA0E2" },
    { id: generateId('elektronik', 'remote-kontrol'), name: "Remote Kontrol", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('elektronik', 'walkie-talkie'), name: "Walkie Talkie", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('elektronik', 'media-player'), name: "Media Player", icon: 'tv', color: "#1BA0E2" },
    { id: generateId('elektronik', 'perangkat-audio'), name: "Perangkat Audio & Speaker", icon: 'headphones', color: "#1BA0E2" },
    { id: generateId('elektronik', 'elektronik-lainnya'), name: "Elektronik Lainnya", icon: 'device-mobile', color: "#1BA0E2" }
  ],
  "Komputer & Aksesoris": [
    { id: generateId('komputer', 'desktop'), name: "Desktop", icon: 'monitor', color: "#5D6D7E" },
    { id: generateId('komputer', 'monitor'), name: "Monitor", icon: 'monitor', color: "#5D6D7E" },
    { id: generateId('komputer', 'komponen-desktop'), name: "Komponen Desktop & Laptop", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'penyimpanan-data'), name: "Penyimpanan Data", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'komponen-network'), name: "Komponen Network", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'software'), name: "Software", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'peralatan-kantor'), name: "Peralatan Kantor", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'printer-scanner'), name: "Printer & Scanner", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'aksesoris-desktop'), name: "Aksesoris Desktop & Laptop", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'keyboard-mouse'), name: "Keyboard & Mouse", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'laptop'), name: "Laptop", icon: 'monitor', color: "#5D6D7E" },
    { id: generateId('komputer', 'gaming'), name: "Gaming", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'audio-computer'), name: "Audio Computer", icon: 'headphones', color: "#5D6D7E" },
    { id: generateId('komputer', 'proyektor'), name: "Proyektor & Aksesoris", icon: 'device-mobile', color: "#5D6D7E" },
    { id: generateId('komputer', 'komputer-lainnya'), name: "Komputer & Aksesoris Lainnya", icon: 'device-mobile', color: "#5D6D7E" }
  ],
  "Handphone & Aksesoris": [
    { id: generateId('handphone', 'kartu-perdana'), name: "Kartu Perdana", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'tablet'), name: "Tablet", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'handphone'), name: "Handphone", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'perangkat-wearable'), name: "Perangkat Wearable", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'perangkat-vr'), name: "Perangkat VR", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'aksesoris-selfie'), name: "Aksesoris Selfie", icon: 'camera', color: "#1BA0E2" },
    { id: generateId('handphone', 'handphone-tablet-aksesoris'), name: "Handphone & Tablet Aksesoris", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'kartu-memori'), name: "Kartu Memori", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'kabel-charger'), name: "Kabel, Charger, & Konverter", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'powerbank'), name: "Powerbank & Baterai", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'casing-skin'), name: "Casing & Skin", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('handphone', 'audio-handphone'), name: "Audio Handphone", icon: 'headphones', color: "#1BA0E2" },
    { id: generateId('handphone', 'handphone-lainnya'), name: "Handphone & Aksesoris Lainnya", icon: 'device-mobile', color: "#1BA0E2" }
  ],
  "Pakaian Pria": [
    { id: generateId('pakaian-pria', 'denim'), name: "Denim", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'hoodie'), name: "Hoodie & Sweatshirt", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'sweater'), name: "Sweater & Cardigan", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'jaket'), name: "Jaket, Mantel, & Rompi", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'jas-formal'), name: "Jas Formal", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'celana-panjang'), name: "Celana Panjang", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'celana-pendek'), name: "Celana Pendek", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'atasan'), name: "Atasan", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'batik'), name: "Batik", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'pakaian-dalam'), name: "Pakaian Dalam", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'pakaian-tidur'), name: "Pakaian Tidur", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'set-pakaian'), name: "Set Pakaian Pria", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'pakaian-tradisional'), name: "Pakaian Tradisional", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'kostum'), name: "Kostum", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'pakaian-kerja'), name: "Pakaian Kerja", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('pakaian-pria', 'pakaian-pria-lainnya'), name: "Pakaian Pria Lainnya", icon: 'shirt', color: "#F1C40F" }
  ],
  "Sepatu Pria": [
    { id: generateId('sepatu-pria', 'sneakers'), name: "Sneakers", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'kaos-kaki'), name: "Kaos Kaki", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'sandal'), name: "Sandal", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'aksesoris-perawatan'), name: "Aksesoris & Perawatan Sepatu", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'boot'), name: "Boot", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'tall-sepatu'), name: "Tall Sepatu", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'slip-on'), name: "Slip-On & Mules", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'sepatu-pria-lainnya'), name: "Sepatu Pria Lainnya", icon: 'shopping-cart', color: "#E67E22" },
    { id: generateId('sepatu-pria', 'sepatu-formal'), name: "Sepatu Formal", icon: 'shopping-cart', color: "#E67E22" }
  ],
  "Tas Pria": [
    { id: generateId('tas-pria', 'tas-selempang'), name: "Tas Selempang & Bahu Pria", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'dompet'), name: "Dompet", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'ransel'), name: "Ransel Pria", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'tas-pinggang'), name: "Tas Pinggang Pria", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'tas-laptop'), name: "Tas Laptop", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'clutch'), name: "Clutch", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'tote-bag'), name: "Tote Bag", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'tas-kerja'), name: "Tas Kerja", icon: 'shopping-cart', color: "#8E44AD" },
    { id: generateId('tas-pria', 'tas-pria-lainnya'), name: "Tas Pria Lainnya", icon: 'shopping-cart', color: "#8E44AD" }
  ],
  "Aksesoris Fashion": [
    { id: generateId('aksesoris', 'cincin'), name: "Cincin", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'anting'), name: "Anting", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'syal'), name: "Syal & Selendang", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'sarung-tangan'), name: "Sarung Tangan", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'aksesoris-rambut'), name: "Aksesoris Rambut", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'gelang-tangan'), name: "Gelang Tangan & Bangle", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'gelang-kaki'), name: "Gelang Kaki", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'topi'), name: "Topi", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'kalung'), name: "Kalung", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'kacamata'), name: "Kacamata & Aksesoris", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'lensa-kontak'), name: "Lensa Kontak & Aksesoris", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'logam-mulia'), name: "Logam Mulia", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'ikat-pinggang'), name: "Ikat Pinggang", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'dasi'), name: "Dasi", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'aksesoris-tambahan'), name: "Aksesoris Tambahan", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'set-paket'), name: "Set & Paket Aksesoris", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'perhiasan-berharga'), name: "Perhiasan Berharga", icon: 'shopping-cart', color: "#E84393" },
    { id: generateId('aksesoris', 'aksesoris-lainnya'), name: "Aksesoris Fashion Lainnya", icon: 'shopping-cart', color: "#E84393" }
  ],
  "Jam Tangan": [
    { id: generateId('jam-tangan', 'jam-wanita'), name: "Jam Tangan Wanita", icon: 'shopping-cart', color: "#F39C12" },
    { id: generateId('jam-tangan', 'jam-pria'), name: "Jam Tangan Pria", icon: 'shopping-cart', color: "#F39C12" },
    { id: generateId('jam-tangan', 'jam-couple'), name: "Jam Tangan Couple", icon: 'shopping-cart', color: "#F39C12" },
    { id: generateId('jam-tangan', 'aksesoris-jam'), name: "Aksesoris Jam Tangan", icon: 'shopping-cart', color: "#F39C12" },
    { id: generateId('jam-tangan', 'jam-lainnya'), name: "Jam Tangan Lainnya", icon: 'shopping-cart', color: "#F39C12" }
  ],
  "Kesehatan": [
    { id: generateId('kesehatan', 'kewanitaan'), name: "Kewanitaan", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'kesehatan-seksual'), name: "Kesehatan Seksual", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'perawatan-mata'), name: "Perawatan Mata", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'suplemen'), name: "Suplemen Makanan", icon: 'package', color: "#2ECC71" },
    { id: generateId('kesehatan', 'obat-obatan'), name: "Obat-obatan & Alat Kesehatan", icon: 'thermometer', color: "#2ECC71" },
    { id: generateId('kesehatan', 'alat-tes'), name: "Alat Tes & Monitor", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'p3k'), name: "P3K", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'alat-bantu'), name: "Alat Bantu Cedera & Disabilitas", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'obat-nyamuk'), name: "Obat Nyamuk", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'popok-dewasa'), name: "Popok Dewasa", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'hand-sanitizer'), name: "Hand Sanitizer", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'minyak-esensial'), name: "Minyak Esensial", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'perawatan-hidung'), name: "Perawatan Hidung & Pernapasan", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'perawatan-telinga'), name: "Perawatan Telinga", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'perawatan-mulut'), name: "Perawatan Mulut", icon: 'activity', color: "#2ECC71" },
    { id: generateId('kesehatan', 'kesehatan-lainnya'), name: "Kesehatan Lainnya", icon: 'activity', color: "#2ECC71" }
  ],
  "Hobi & Koleksi": [
    { id: generateId('hobi', 'aksesoris-hewan'), name: "Aksesoris Hewan Peliharaan", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'litter-toilet'), name: "Litter & Toilet", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'grooming-hewan'), name: "Grooming Hewan", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'pakaian-hewan'), name: "Pakaian & Aksesoris Hewan", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'perawatan-kesehatan-hewan'), name: "Perawatan Kesehatan Hewan", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'makanan-hewan'), name: "Makanan Hewan", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'koleksi'), name: "Koleksi", icon: 'archive', color: "#1ABC9C" },
    { id: generateId('hobi', 'mainan-games'), name: "Mainan & Games", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'cd-dvd'), name: "CD, DVD & Bluray", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'alat-musik'), name: "Alat & Aksesoris Musik", icon: 'music', color: "#1ABC9C" },
    { id: generateId('hobi', 'piringan-hitam'), name: "Piringan Hitam", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'album-foto'), name: "Album Foto", icon: 'camera', color: "#1ABC9C" },
    { id: generateId('hobi', 'perlengkapan-menjahit'), name: "Perlengkapan Menjahit", icon: 'star', color: "#1ABC9C" },
    { id: generateId('hobi', 'hobi-lainnya'), name: "Hobi & Koleksi Lainnya", icon: 'star', color: "#1ABC9C" }
  ]
}

// Komponen CategoryItem - icon sama untuk mobile dan desktop
const CategoryItem = ({ category, onClick, isExpandedView = false, productCount }: {
  category: Category,
  onClick?: (e: React.MouseEvent<Element>) => void,
  isExpandedView?: boolean,
  productCount?: number
}) => (
  <motion.div
    className={`
      flex flex-col items-center justify-center rounded-lg transition-all duration-200
      border border-gray-200 bg-white hover:bg-gray-50 shadow-sm
      ${isExpandedView ? 'p-2 w-[100px] h-[100px]' : 'p-1.5 w-[80px] h-[80px] mx-0.5'}
      overflow-hidden cursor-pointer
    `}
    initial={{ opacity: 0, y: 5 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    onClick={(e: React.MouseEvent) => {
      e.preventDefault()
      onClick?.(e)
    }}
  >
    <div className={`flex items-center justify-center ${isExpandedView ? 'mb-2' : 'mb-1'}`} style={{ height: '32px', width: '100%' }}>
      <div
        className="flex items-center justify-center w-8 h-8 text-2xl"
        style={{ minWidth: '32px' }}
      >
        {getIcon(category.icon)}
      </div>
    </div>
    <div className="w-full px-1 flex flex-col justify-center items-center">
      <span className={`
        text-center text-gray-800 leading-tight break-words
        ${isExpandedView ? 'text-xs' : 'text-[10px] line-clamp-2'}
        w-full
      `}>
        {category.name}
      </span>
      {/* Count produk dihilangkan sesuai permintaan user */}
    </div>
  </motion.div>
)

// Komponen SeeAllItem
const SeeAllItem = ({ onClick, isExpanded }: { onClick: (e: React.MouseEvent) => void, isExpanded: boolean }) => (
  <motion.div
    className="flex flex-col items-center justify-center rounded-lg transition-all duration-200
               border border-gray-200 bg-white hover:bg-gray-50 shadow-sm
               p-1.5 w-[80px] h-[80px] mx-0.5 overflow-hidden cursor-pointer"
    initial={{ opacity: 0, y: 5 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    onClick={onClick}
  >
    <div className="flex items-center justify-center mb-1" style={{ height: '32px', width: '100%' }}>
      <div className="flex items-center justify-center w-8 h-8 text-2xl" style={{ minWidth: '32px' }}>
        ⊡
      </div>
    </div>
    <div className="w-full px-1 flex justify-center">
      <span className="text-center text-gray-800 leading-tight break-words text-[10px] line-clamp-2 w-full">
        Lihat Semua
      </span>
    </div>
  </motion.div>
)

export default function SellzioCategories({ products }: SellzioCategoriesProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [visibleCategories, setVisibleCategories] = useState(allCategories.slice(0, 9))
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [showSubcategoryView, setShowSubcategoryView] = useState(false)
  const [sortedCategories, setSortedCategories] = useState(allCategories)
  const [showScrollHint, setShowScrollHint] = useState(false)
  const [isScrolling, setIsScrolling] = useState(false)
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const initialShowTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const showIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const [isMobile, setIsMobile] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const mobileContainerRef = useRef<HTMLDivElement>(null)

  // Fungsi untuk menghitung jumlah produk per subcategory
  // PENTING: Menggunakan product.subcategory, BUKAN product.category
  const getProductCount = (subcategoryName: string): number => {
    return products.filter(product =>
      product.subcategory.toLowerCase() === subcategoryName.toLowerCase()
    ).length;
  };

  // Fungsi untuk menghitung total produk dalam main category
  const getCategoryProductCount = (categoryName: string): number => {
    const categorySubcategories = getSubcategories(categoryName);
    return categorySubcategories.reduce((total, subcategory) => {
      return total + getProductCount(subcategory.name);
    }, 0);
  };

  // Detect client-side and mobile
  useEffect(() => {
    setIsClient(true)
    const checkMobile = () => {
      if (typeof window !== 'undefined') {
        setIsMobile(window.innerWidth < 768)
      }
    }

    checkMobile()
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', checkMobile)
      return () => window.removeEventListener('resize', checkMobile)
    }
  }, [])

  // Set up initial show timeout dan interval untuk scroll hint
  useEffect(() => {
    if (isMobile) {
      // Tampilkan tombol pertama kali setelah 7 detik
      initialShowTimeoutRef.current = setTimeout(() => {
        if (!isExpanded) {
          showViewAllButton();
        }

        // Set interval untuk menampilkan tombol setiap 15 detik
        showIntervalRef.current = setInterval(() => {
          if (!isScrolling && !isExpanded) {
            showViewAllButton();
          }
        }, 15000);
      }, 7000);

      // Add scroll event listener
      const container = mobileContainerRef.current;
      if (container) {
        container.addEventListener('scroll', handleScroll);
      }

      // Cleanup function
      return () => {
        if (initialShowTimeoutRef.current) clearTimeout(initialShowTimeoutRef.current);
        if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);
        if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current);
        if (showIntervalRef.current) clearInterval(showIntervalRef.current);
        if (container) container.removeEventListener('scroll', handleScroll);
      };
    }
  }, [isMobile, isScrolling, isExpanded])

  // Fungsi untuk menangani klik kategori - sama seperti Velozio
  const handleCategoryClick = (e: React.MouseEvent, category: Category) => {
    e.preventDefault();
    if (!category) return;

    // Untuk tampilan mobile, buka subcategory view
    if (isMobile) {
      // Update urutan kategori dengan yang dipilih di paling atas
      const newSortedCategories = [
        category,
        ...allCategories.filter((cat: Category) => cat.id !== category.id)
      ];
      setSortedCategories(newSortedCategories);

      setSelectedCategory(category);
      setShowSubcategoryView(true);
      // Disable scrolling pada body saat subcategory view terbuka
      if (typeof document !== 'undefined') {
        document.body.style.overflow = 'hidden';
      }
      return;
    }

    // Update urutan kategori dengan yang dipilih di paling atas untuk semua device
    const newSortedCategories = [
      category,
      ...allCategories.filter((cat: Category) => cat.id !== category.id)
    ];
    setSortedCategories(newSortedCategories);

    const hasSubcategories = category.name in subCategories &&
      subCategories[category.name]?.length > 0;

    if (hasSubcategories) {
      setSelectedCategory(category);
    } else {
      // Jika tidak ada subkategori, lakukan aksi default
      console.log('Kategori dipilih:', category.name);
      // Tambahkan logika navigasi atau tindakan lain yang diperlukan
    }
  };

  // Fungsi untuk toggle expand - sama seperti Velozio
  const toggleExpand = (e: React.MouseEvent) => {
    e.preventDefault();

    if (isMobile) {
      // Jika di mobile, langsung arahkan ke subcategory view dengan kategori pertama
      if (allCategories.length > 0) {
        const firstCategory = allCategories[0];
        const newSortedCategories = [
          firstCategory,
          ...allCategories.filter((cat: Category) => cat.id !== firstCategory.id)
        ];
        setSortedCategories(newSortedCategories);
        setSelectedCategory(firstCategory);
        setShowSubcategoryView(true);
        if (typeof document !== 'undefined') {
          document.body.style.overflow = 'hidden';
        }
      }
      return;
    }

    // Untuk desktop/tablet, tetap gunakan logika expand biasa
    const newExpandedState = !isExpanded;
    setIsExpanded(newExpandedState);

    if (newExpandedState && mobileContainerRef.current) {
      mobileContainerRef.current.scrollLeft = 0;
    }

    setVisibleCategories(newExpandedState ? allCategories : allCategories.slice(0, 9));
  };

  // Fungsi untuk menutup subcategory view
  const handleCloseSubcategoryView = () => {
    setShowSubcategoryView(false);
    // Enable scrolling kembali pada body
    if (typeof document !== 'undefined') {
      document.body.style.overflow = '';
    }
  };

  // Mengambil daftar subkategori
  const getSubcategories = (categoryName: string) => {
    if (!categoryName || !(categoryName in subCategories)) {
      console.log('Kategori tidak ditemukan atau tidak valid:', categoryName);
      return [];
    }
    return subCategories[categoryName];
  };

  // FIXED: Mengambil produk untuk subcategory tertentu dari sampleProducts di page.tsx
  const getSubcategoryProducts = (subcategoryName: string) => {
    // Dispatch event untuk mendapatkan data dari page.tsx
    if (typeof window !== 'undefined') {
      const getProductsEvent = new CustomEvent('getProductsByCategory', {
        detail: { category: subcategoryName }
      });
      window.dispatchEvent(getProductsEvent);
    }

    // Return empty array karena data akan dikirim melalui event
    // Data sebenarnya akan diterima melalui subcategorySearch event
    return [];
  };

  // getAllProducts function is now defined directly in page.tsx

  // Fungsi untuk menangani klik tombol kembali
  const handleBackClick = () => {
    setSelectedCategory(null);
  };

  // Fungsi untuk menangani klik subkategori
  const handleSubcategoryClick = (subcategory: Category) => {
    if (!subcategory) return;

    console.log('Subkategori dipilih:', subcategory.name);

    // Tutup subcategory view terlebih dahulu
    const currentCategory = selectedCategory;
    setSelectedCategory(null);
    setShowSubcategoryView(false);
    if (typeof document !== 'undefined') {
      document.body.style.overflow = '';
    }

    // Trigger search di halaman utama dengan subcategory name
    // Kita perlu memanggil fungsi search dari parent component
    if (typeof window !== 'undefined') {
      console.log('🔥 CATEGORIES: Starting subcategory click for:', subcategory.name);
      console.log('🔥 CATEGORIES: Products available:', products.length);

      const allSubs = getSubcategories(currentCategory?.name || '');
      // FIXED: Gunakan data produk yang sudah tersedia dari props, filter berdasarkan subcategory
      const subcategoryProducts = products.filter(product =>
        product.subcategory.toLowerCase() === subcategory.name.toLowerCase()
      );

      console.log('🔥 CATEGORIES: Filtered products:', subcategoryProducts.length);
      console.log('🔥 CATEGORIES: Filtered product names:', subcategoryProducts.map(p => p.name));

      console.log('🔥 CATEGORIES: Dispatching event with data:', {
        query: subcategory.name,
        category: currentCategory?.name,
        selectedSubcategory: subcategory.name,
        allSubcategories: allSubs,
        products: subcategoryProducts
      });

      // Dispatch custom event untuk komunikasi dengan parent
      const searchEvent = new CustomEvent('subcategorySearch', {
        detail: {
          query: subcategory.name,
          category: currentCategory?.name,
          selectedSubcategory: subcategory.name,
          allSubcategories: allSubs,
          products: subcategoryProducts
        }
      });
      window.dispatchEvent(searchEvent);
      console.log('🔥 CATEGORIES: Event dispatched!');
    }
  };

  // Animasi scroll hint untuk mobile
  const mobileScrollHintAnimation = {
    x: [0, 3, 0],
    transition: {
      repeat: Infinity,
      duration: 1.5,
      repeatType: "loop" as const,
      ease: "easeInOut"
    }
  };

  // Fungsi untuk menampilkan tombol View All
  const showViewAllButton = () => {
    // Jangan tampilkan tombol jika dalam mode expanded
    if (isExpanded) {
      setShowScrollHint(false);
      return;
    }

    // Hapus timeout yang ada
    if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current);

    // Tampilkan tombol
    setShowScrollHint(true);

    // Sembunyikan setelah 5 detik
    hideTimeoutRef.current = setTimeout(() => {
      setShowScrollHint(false);
    }, 5000);
  };

  // Handle scroll untuk mobile
  const handleScroll = () => {
    if (!mobileContainerRef.current || isExpanded) return;

    const { scrollLeft, scrollWidth, clientWidth } = mobileContainerRef.current;
    const isAtEnd = scrollLeft + clientWidth >= scrollWidth - 5; // Sedikit toleransi

    // Sembunyikan tombol View All saat scroll
    setShowScrollHint(false);
    setIsScrolling(true);

    // Hapus semua timeout yang ada
    if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);
    if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current);

    // Set timeout untuk menampilkan tombol View All setelah 15 detik tidak ada scroll
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
      if (!isAtEnd && !isExpanded) { // Hanya tampilkan jika tidak di ujung kanan dan tidak expanded
        showViewAllButton();
      }
    }, 15000);
  };

  // Prevent hydration mismatch by not rendering until client-side
  if (!isClient) {
    return (
      <div className="sellzio-categories-section">
        <div className="container mx-auto max-w-6xl px-0 overflow-visible">
          <div className="desktop-categories-grid">
            {allCategories.map((category, index) => (
              <CategoryItem
                key={`desktop-${category.id}-${index}`}
                category={category}
                onClick={(e) => handleCategoryClick(e, category)}
                isExpandedView={false}
                productCount={getCategoryProductCount(category.name)}
              />
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="sellzio-categories-section">
      {/* Desktop/Tablet Subcategory Expanded View - Full screen tanpa popup */}
      {selectedCategory && !isMobile && (
        <div className="fixed inset-0 bg-white z-50 overflow-y-auto">
          {/* Subcategory Grid - Full view tanpa header */}
          <div className="p-6">
            <div className="grid grid-cols-6 gap-4 max-w-6xl mx-auto">
              {getSubcategories(selectedCategory.name).map((subCategory: Category, idx: number) => {
                const productCount = getProductCount(subCategory.name);
                const isDisabled = productCount === 0;

                return (
                  <div
                    key={idx}
                    className={`flex flex-col items-center p-4 rounded-lg transition-colors min-h-[100px] justify-between ${
                      isDisabled
                        ? 'opacity-50 cursor-not-allowed'
                        : 'cursor-pointer hover:bg-gray-50'
                    }`}
                    onClick={!isDisabled ? () => handleSubcategoryClick(subCategory) : undefined}
                    style={{
                      opacity: isDisabled ? 0.5 : 1,
                      cursor: isDisabled ? 'not-allowed' : 'pointer',
                      pointerEvents: isDisabled ? 'none' : 'auto'
                    }}
                  >
                    <div
                      className="w-16 h-16 rounded-full flex items-center justify-center text-3xl mb-2"
                      style={{ backgroundColor: `${selectedCategory.color}1a` }}
                    >
                      {getSubcategoryIcon(subCategory.name)}
                    </div>
                    <span className={`text-sm text-center font-medium line-clamp-2 ${
                      isDisabled ? 'text-gray-400' : 'text-gray-700'
                    }`}>
                      {subCategory.name}
                    </span>
                    {/* Count produk dihilangkan sesuai permintaan user */}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      <div className="container mx-auto max-w-6xl px-0 overflow-visible">
        {/* Mobile View */}
        {isMobile ? (
          <>
            <div className={`relative categories-container-wrapper ${!isExpanded ? 'has-blur' : ''}`}>
              {!isExpanded && (
                <div
                  ref={mobileContainerRef}
                  className="flex overflow-x-auto pb-3 hide-scrollbar categories-container"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                    WebkitOverflowScrolling: 'touch',
                    paddingRight: '5px'
                  }}
                >
                  <div className="flex gap-2 relative pr-4">
                    <AnimatePresence>
                      {visibleCategories.map((category, index) => (
                        <CategoryItem
                          key={index}
                          category={category}
                          onClick={(e) => handleCategoryClick(e, category)}
                          productCount={getCategoryProductCount(category.name)}
                        />
                      ))}
                      {!isExpanded && (
                        <div className="pr-2">
                          <SeeAllItem
                            onClick={(e: React.MouseEvent) => toggleExpand(e)}
                            isExpanded={isExpanded}
                          />
                        </div>
                      )}
                    </AnimatePresence>
                  </div>

                  {/* Animasi panah untuk scroll hint di mobile */}
                  {showScrollHint && !isScrolling && (
                    <div
                      className="absolute right-2 top-[45%] -translate-y-1/2 z-10 h-8 bg-[rgba(255,245,240,0.95)] border border-[#FFDFD1] rounded-[16px] shadow-md flex items-center px-2 py-0 cursor-pointer"
                      onClick={(e: React.MouseEvent) => toggleExpand(e)}
                    >
                      <span className="text-[11px] font-semibold text-[#FF5722] mr-1">View All</span>
                      <motion.div
                        className="flex items-center justify-center text-[#FF5722]"
                        animate={mobileScrollHintAnimation}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                      </motion.div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Mobile Expanded View */}
            {isExpanded && !showSubcategoryView && (
              <div
                className="fixed inset-0 bg-white z-50 overflow-y-auto"
                style={{ paddingTop: '60px' }}
              >
                <div
                  className="grid grid-flow-col auto-cols-[100px] gap-3 justify-start w-max mx-auto"
                  style={{
                    gridAutoFlow: 'column',
                    gridTemplateRows: 'repeat(2, 100px)',
                    padding: '0.5rem 1rem',
                    margin: '0 auto',
                    width: 'auto',
                    minWidth: '100%',
                    display: 'inline-grid'
                  } as React.CSSProperties}
                >
                  <AnimatePresence>
                    {allCategories.map((category, index) => (
                      <CategoryItem
                        key={index}
                        category={category}
                        isExpandedView={true}
                        onClick={(e) => handleCategoryClick(e, category)}
                        productCount={getCategoryProductCount(category.name)}
                      />
                    ))}
                  </AnimatePresence>
                </div>
              </div>
            )}

            {/* Mobile Subcategory View */}
            {showSubcategoryView && selectedCategory && (
              <SubcategoryView
                category={selectedCategory}
                subcategories={getSubcategories(selectedCategory.name)}
                allCategories={allCategories}
                products={products}
                onBack={handleCloseSubcategoryView}
                onSubcategoryClick={handleSubcategoryClick}
                onCategoryChange={(newCategory) => {
                  setSelectedCategory(newCategory);
                  // Update urutan kategori dengan yang dipilih di paling atas
                  const newSortedCategories = [
                    newCategory,
                    ...allCategories.filter((cat: Category) => cat.id !== newCategory.id)
                  ];
                  setSortedCategories(newSortedCategories);
                }}
              />
            )}
          </>
        ) : (
          /* Desktop View */
          <div className="desktop-categories-grid">
            {allCategories.map((category, index) => (
              <CategoryItem
                key={`desktop-${category.id}-${index}`}
                category={category}
                onClick={(e) => handleCategoryClick(e, category)}
                isExpandedView={false}
                productCount={getCategoryProductCount(category.name)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
